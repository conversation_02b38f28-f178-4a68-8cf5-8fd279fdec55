#!/usr/bin/env python3
"""
Test script to verify the SeleniumBase chrome_options fix
This script tests that the Driver can be created without the chrome_options error
"""

import sys
import os
from unittest.mock import Mock, patch

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_chrome_options_conversion():
    """Test that Chrome options are properly converted to chromium_arg format"""
    print("=== Testing Chrome Options Conversion ===")
    
    try:
        # Mock the Chrome options object
        mock_chrome_options = Mock()
        mock_chrome_options.arguments = [
            '--user-agent=test-agent',
            '--disable-blink-features=AutomationControlled',
            '--no-sandbox'
        ]
        mock_chrome_options.experimental_options = {
            'excludeSwitches': ['enable-automation'],
            'useAutomationExtension': False,
            'prefs': {'profile.default_content_setting_values.notifications': 2}
        }
        
        # Test the conversion logic
        chrome_args = []
        if hasattr(mock_chrome_options, 'arguments'):
            chrome_args.extend(mock_chrome_options.arguments)
        
        if hasattr(mock_chrome_options, 'experimental_options'):
            for key, value in mock_chrome_options.experimental_options.items():
                if key == 'excludeSwitches':
                    for switch in value:
                        chrome_args.append(f'--disable-{switch}')
                elif key == 'useAutomationExtension':
                    if not value:
                        chrome_args.append('--disable-automation')
        
        # Verify the conversion worked
        expected_args = [
            '--user-agent=test-agent',
            '--disable-blink-features=AutomationControlled',
            '--no-sandbox',
            '--disable-enable-automation',
            '--disable-automation'
        ]
        
        for expected_arg in expected_args:
            assert expected_arg in chrome_args, f"Expected argument {expected_arg} not found in {chrome_args}"
        
        print("✓ Chrome options conversion test passed")
        print(f"  Converted {len(chrome_args)} arguments successfully")
        return True
        
    except Exception as e:
        print(f"✗ Chrome options conversion test failed: {e}")
        return False

def test_chromium_arg_format():
    """Test that chromium_arg is properly formatted for SeleniumBase"""
    print("\n=== Testing Chromium Arg Format ===")
    
    try:
        chrome_args = [
            '--user-agent=test-agent',
            '--disable-blink-features=AutomationControlled',
            '--no-sandbox',
            '--disable-automation'
        ]
        
        # Test joining arguments
        chromium_arg = ','.join(chrome_args)
        
        # Verify format
        assert ',' in chromium_arg, "Arguments should be comma-separated"
        assert '--user-agent=test-agent' in chromium_arg, "User agent should be preserved"
        assert '--disable-automation' in chromium_arg, "Automation disabling should be included"
        
        # Test splitting back
        split_args = chromium_arg.split(',')
        assert len(split_args) == len(chrome_args), "Split should return same number of arguments"
        
        print("✓ Chromium arg format test passed")
        print(f"  Formatted: {chromium_arg[:50]}...")
        return True
        
    except Exception as e:
        print(f"✗ Chromium arg format test failed: {e}")
        return False

def test_duplicate_removal():
    """Test that duplicate arguments are properly removed"""
    print("\n=== Testing Duplicate Removal ===")
    
    try:
        chrome_args = [
            '--user-agent=test-agent',
            '--no-sandbox',
            '--disable-automation',
            '--no-sandbox',  # Duplicate
            '--user-agent=test-agent'  # Duplicate
        ]
        
        # Remove duplicates while preserving order
        seen = set()
        unique_args = []
        for arg in chrome_args:
            if arg not in seen:
                seen.add(arg)
                unique_args.append(arg)
        
        # Verify duplicates were removed
        assert len(unique_args) == 3, f"Expected 3 unique args, got {len(unique_args)}"
        assert unique_args.count('--no-sandbox') == 1, "Should have only one --no-sandbox"
        assert unique_args.count('--user-agent=test-agent') == 1, "Should have only one user-agent"
        
        # Verify order is preserved
        assert unique_args[0] == '--user-agent=test-agent', "First unique arg should be user-agent"
        assert unique_args[1] == '--no-sandbox', "Second unique arg should be no-sandbox"
        assert unique_args[2] == '--disable-automation', "Third unique arg should be disable-automation"
        
        print("✓ Duplicate removal test passed")
        print(f"  Reduced from {len(chrome_args)} to {len(unique_args)} arguments")
        return True
        
    except Exception as e:
        print(f"✗ Duplicate removal test failed: {e}")
        return False

def run_all_tests():
    """Run all SeleniumBase fix tests"""
    print("Starting SeleniumBase chrome_options fix verification tests...\n")
    
    tests = [
        test_chrome_options_conversion,
        test_chromium_arg_format,
        test_duplicate_removal
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            failed += 1
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Total: {passed + failed}")
    
    if failed == 0:
        print("🎉 All tests passed! The SeleniumBase chrome_options fix is working correctly.")
        print("The 'chrome_options' error should now be resolved.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the fix.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)

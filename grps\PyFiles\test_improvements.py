#!/usr/bin/env python3
"""
Test script to verify the improvements made to groups.py
This script tests the key fixes without requiring a full browser session
"""

import json
import os
import sys
import logging
from unittest.mock import Mock, MagicMock

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Mock the enhanced driver imports to avoid import errors
sys.modules['updated_groups'] = Mock()
sys.modules['enhanced_proxy_manager'] = Mock()
sys.modules['fivesim_integration'] = Mock()

# Import the groups module
try:
    from groups import Worker, Driver
    print("✓ Successfully imported Worker and Driver classes")
except ImportError as e:
    print(f"✗ Failed to import classes: {e}")
    sys.exit(1)

def test_worker_initialization():
    """Test that Worker class initializes correctly with new attributes"""
    print("\n=== Testing Worker Initialization ===")
    
    try:
        worker = Worker(['test_action'])
        
        # Check that new attributes exist
        assert hasattr(worker, 'browser'), "Worker should have browser attribute"
        assert hasattr(worker, '_enhanced_driver'), "Worker should have _enhanced_driver attribute"
        assert worker.browser is None, "Browser should be None initially"
        assert worker._enhanced_driver is None, "Enhanced driver should be None initially"
        
        print("✓ Worker initialization test passed")
        return True
    except Exception as e:
        print(f"✗ Worker initialization test failed: {e}")
        return False

def test_driver_reference_setup():
    """Test the _setup_driver_references method"""
    print("\n=== Testing Driver Reference Setup ===")
    
    try:
        worker = Worker(['test_action'])
        
        # Create a mock driver
        mock_driver = Mock()
        mock_driver._enhanced_driver = Mock()
        
        # Test setup
        worker._setup_driver_references(mock_driver)
        
        assert worker.browser == mock_driver, "Browser reference should be set"
        assert worker._enhanced_driver == mock_driver._enhanced_driver, "Enhanced driver reference should be set"
        
        print("✓ Driver reference setup test passed")
        return True
    except Exception as e:
        print(f"✗ Driver reference setup test failed: {e}")
        return False

def test_language_status_methods():
    """Test the language status tracking methods"""
    print("\n=== Testing Language Status Methods ===")
    
    try:
        worker = Worker(['test_action'])
        
        # Mock browser
        worker.browser = Mock()
        worker.browser.email = "<EMAIL>"
        
        # Test suspicious activity methods
        assert hasattr(worker, '_is_suspicious_activity_cleared'), "Should have suspicious activity check method"
        assert hasattr(worker, '_update_suspicious_activity_status'), "Should have suspicious activity update method"
        
        print("✓ Language status methods test passed")
        return True
    except Exception as e:
        print(f"✗ Language status methods test failed: {e}")
        return False

def test_captcha_verification_fallback():
    """Test that CaptchaVerif method handles missing enhanced driver"""
    print("\n=== Testing Captcha Verification Fallback ===")
    
    try:
        worker = Worker(['test_action'])
        
        # Mock browser without enhanced driver
        worker.browser = Mock()
        worker.browser.find_xpath = Mock(side_effect=Exception("Element not found"))
        worker._enhanced_driver = None
        
        # This should not raise an exception
        result = worker.CaptchaVerif()
        assert isinstance(result, bool), "CaptchaVerif should return boolean"
        
        print("✓ Captcha verification fallback test passed")
        return True
    except Exception as e:
        print(f"✗ Captcha verification fallback test failed: {e}")
        return False

def test_enhanced_driver_compatibility():
    """Test compatibility with and without enhanced driver"""
    print("\n=== Testing Enhanced Driver Compatibility ===")

    try:
        worker = Worker(['test_action'])

        # Test with enhanced driver
        mock_enhanced = Mock()
        mock_enhanced.find_xpath_silent = Mock(return_value=None)
        worker._enhanced_driver = mock_enhanced
        worker.browser = Mock()

        # This should use enhanced driver method
        result = worker.CaptchaVerif()
        mock_enhanced.find_xpath_silent.assert_called()

        # Test without enhanced driver
        worker._enhanced_driver = None
        worker.browser.find_xpath = Mock(side_effect=Exception("Not found"))

        # This should use fallback method
        result = worker.CaptchaVerif()
        assert isinstance(result, bool), "Should still return boolean"

        print("✓ Enhanced driver compatibility test passed")
        return True
    except Exception as e:
        print(f"✗ Enhanced driver compatibility test failed: {e}")
        return False

def test_chrome_sync_status_tracking():
    """Test Chrome sync status tracking methods"""
    print("\n=== Testing Chrome Sync Status Tracking ===")

    try:
        worker = Worker(['test_action'])

        # Mock browser
        worker.browser = Mock()
        worker.browser.email = "<EMAIL>"

        # Test Chrome sync methods exist
        assert hasattr(worker, '_is_chrome_sync_enabled'), "Should have Chrome sync check method"
        assert hasattr(worker, '_update_chrome_sync_status'), "Should have Chrome sync update method"

        print("✓ Chrome sync status tracking test passed")
        return True
    except Exception as e:
        print(f"✗ Chrome sync status tracking test failed: {e}")
        return False

def test_silent_detection_system():
    """Test the silent detection system"""
    print("\n=== Testing Silent Detection System ===")

    try:
        worker = Worker(['test_action'])

        # Test silent detection methods exist
        assert hasattr(worker, '_has_silent_detection'), "Should have silent detection check method"
        assert hasattr(worker, '_find_element_silent'), "Should have silent element find method"

        # Test with no enhanced driver
        worker._enhanced_driver = None
        worker.browser = Mock()
        worker.browser.wait_xpath_presence = Mock(side_effect=Exception("Not found"))

        # This should not raise an exception
        result = worker._find_element_silent("//test", 'xpath')
        assert result is None, "Should return None when element not found"

        print("✓ Silent detection system test passed")
        return True
    except Exception as e:
        print(f"✗ Silent detection system test failed: {e}")
        return False

def run_all_tests():
    """Run all tests and report results"""
    print("Starting improvement verification tests...\n")
    
    tests = [
        test_worker_initialization,
        test_driver_reference_setup,
        test_language_status_methods,
        test_captcha_verification_fallback,
        test_enhanced_driver_compatibility,
        test_chrome_sync_status_tracking,
        test_silent_detection_system
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            failed += 1
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Total: {passed + failed}")
    
    if failed == 0:
        print("🎉 All tests passed! The improvements are working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the improvements.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)

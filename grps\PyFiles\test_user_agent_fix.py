#!/usr/bin/env python3
"""
Test script to verify the User Agent integration fix for SeleniumBase
This script tests that user agents are properly prioritized and applied
"""

import sys
import os

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_user_agent_priority():
    """Test that user agent from chrome_options takes priority over existing args"""
    print("=== Testing User Agent Priority ===")
    
    try:
        # Simulate the Chrome arguments merging logic
        chrome_args = ['--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36']
        existing_args = ['--no-sandbox', '--user-agent=old-user-agent', '--disable-dev-shm-usage']
        
        # Filter out existing user-agent arguments
        filtered_existing_args = []
        for arg in existing_args:
            if not arg.startswith('--user-agent='):
                filtered_existing_args.append(arg)
            else:
                print(f"  Replacing existing user agent: {arg}")
        
        chrome_args.extend(filtered_existing_args)
        
        # Remove duplicates while preserving order
        seen = set()
        unique_args = []
        for arg in chrome_args:
            if arg.startswith('--user-agent='):
                if '--user-agent=' not in str(seen):
                    seen.add('--user-agent=')
                    unique_args.append(arg)
                    print(f"  Using user agent: {arg}")
            elif arg not in seen:
                seen.add(arg)
                unique_args.append(arg)
        
        # Verify the correct user agent is used
        user_agent_args = [arg for arg in unique_args if arg.startswith('--user-agent=')]
        assert len(user_agent_args) == 1, f"Expected 1 user agent arg, got {len(user_agent_args)}"
        assert 'Mozilla/5.0' in user_agent_args[0], "Should use the new user agent, not the old one"
        assert 'old-user-agent' not in str(unique_args), "Old user agent should be removed"
        
        print("✓ User agent priority test passed")
        print(f"  Final args: {len(unique_args)} arguments")
        print(f"  User agent: {user_agent_args[0]}")
        return True
        
    except Exception as e:
        print(f"✗ User agent priority test failed: {e}")
        return False

def test_user_agent_validation():
    """Test user agent validation logic"""
    print("\n=== Testing User Agent Validation ===")
    
    try:
        # Test cases for user agent validation
        test_cases = [
            {
                'expected': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'actual': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'should_match': True
            },
            {
                'expected': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
                'actual': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'should_match': False
            },
            {
                'expected': 'Chrome/120.0.0.0',
                'actual': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'should_match': True
            }
        ]
        
        for i, test_case in enumerate(test_cases):
            expected = test_case['expected']
            actual = test_case['actual']
            should_match = test_case['should_match']
            
            matches = expected in actual
            
            if matches == should_match:
                print(f"  ✓ Test case {i+1}: {'Match' if matches else 'No match'} as expected")
            else:
                print(f"  ✗ Test case {i+1}: Expected {'match' if should_match else 'no match'}, got {'match' if matches else 'no match'}")
                return False
        
        print("✓ User agent validation test passed")
        return True
        
    except Exception as e:
        print(f"✗ User agent validation test failed: {e}")
        return False

def test_chrome_args_deduplication():
    """Test that Chrome arguments are properly deduplicated"""
    print("\n=== Testing Chrome Args Deduplication ===")
    
    try:
        # Test with duplicate arguments
        chrome_args = [
            '--user-agent=test-agent',
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--no-sandbox',  # Duplicate
            '--disable-automation',
            '--disable-dev-shm-usage'  # Duplicate
        ]
        
        # Remove duplicates while preserving order
        seen = set()
        unique_args = []
        for arg in chrome_args:
            if arg.startswith('--user-agent='):
                if '--user-agent=' not in str(seen):
                    seen.add('--user-agent=')
                    unique_args.append(arg)
            elif arg not in seen:
                seen.add(arg)
                unique_args.append(arg)
        
        # Verify deduplication worked
        assert len(unique_args) == 4, f"Expected 4 unique args, got {len(unique_args)}"
        assert unique_args.count('--no-sandbox') == 1, "Should have only one --no-sandbox"
        assert unique_args.count('--disable-dev-shm-usage') == 1, "Should have only one --disable-dev-shm-usage"
        assert '--user-agent=test-agent' in unique_args, "User agent should be preserved"
        
        # Verify order is preserved
        expected_order = ['--user-agent=test-agent', '--no-sandbox', '--disable-dev-shm-usage', '--disable-automation']
        assert unique_args == expected_order, f"Order not preserved: {unique_args}"
        
        print("✓ Chrome args deduplication test passed")
        print(f"  Reduced from {len(chrome_args)} to {len(unique_args)} arguments")
        return True
        
    except Exception as e:
        print(f"✗ Chrome args deduplication test failed: {e}")
        return False

def run_all_tests():
    """Run all user agent fix tests"""
    print("Starting User Agent integration fix verification tests...\n")
    
    tests = [
        test_user_agent_priority,
        test_user_agent_validation,
        test_chrome_args_deduplication
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            failed += 1
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Total: {passed + failed}")
    
    if failed == 0:
        print("🎉 All tests passed! The User Agent integration fix is working correctly.")
        print("The user agent should now be properly applied to SeleniumBase.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the fix.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)

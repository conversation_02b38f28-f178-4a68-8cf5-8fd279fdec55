"""
5sim API Integration Module for Gmail Phone Verification
Provides automatic phone number acquisition and SMS verification for Gmail login automation.
"""

import requests
import json
import time
import logging
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from enum import Enum


class FiveSimError(Exception):
    """Custom exception for 5sim API errors"""
    pass


class OrderStatus(Enum):
    """5sim order status enumeration"""
    PENDING = "PENDING"
    RECEIVED = "RECEIVED"
    CANCELED = "CANCELED"
    TIMEOUT = "TIMEOUT"
    FINISHED = "FINISHED"
    BANNED = "BANNED"


@dataclass
class SMSMessage:
    """SMS message data structure"""
    id: int
    created_at: str
    date: str
    sender: str
    text: str
    code: str


@dataclass
class PhoneOrder:
    """Phone number order data structure"""
    id: int
    phone: str
    operator: str
    product: str
    price: float
    status: str
    expires: str
    sms: List[SMSMessage]
    created_at: str
    country: str


class FiveSimClient:
    """
    5sim API client for phone number verification
    Integrates with existing Gmail automation workflow
    """
    
    def __init__(self, api_key: str, logger: Optional[logging.Logger] = None):
        """
        Initialize 5sim client
        
        Args:
            api_key: 5sim API key
            logger: Logger instance for debugging
        """
        self.api_key = api_key
        self.base_url = "https://5sim.net/v1"
        self.logger = logger or logging.getLogger(__name__)
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {api_key}',
            'Accept': 'application/json'
        })
        
        # Configuration
        self.default_country = "any"  # Can be configured per account
        self.default_operator = "any"
        self.product = "google"  # For Gmail verification
        self.max_wait_time = 300  # 5 minutes max wait for SMS
        self.check_interval = 10  # Check for SMS every 10 seconds
        
    def get_balance(self) -> float:
        """Get account balance"""
        try:
            response = self.session.get(f"{self.base_url}/user/profile")
            response.raise_for_status()
            data = response.json()
            balance = data.get('balance', 0)
            self.logger.info(f"5sim account balance: ${balance}")
            return balance
        except Exception as e:
            self.logger.error(f"Failed to get 5sim balance: {str(e)}")
            raise FiveSimError(f"Failed to get balance: {str(e)}")
    
    def get_available_numbers(self, country: str = None, operator: str = None) -> Dict[str, Any]:
        """Get available phone numbers for the product"""
        try:
            country = country or self.default_country
            operator = operator or self.default_operator
            
            url = f"{self.base_url}/guest/products/{country}/{operator}"
            response = self.session.get(url)
            response.raise_for_status()
            
            data = response.json()
            google_info = data.get('google', {})
            
            if not google_info:
                self.logger.warning(f"No Google verification numbers available for {country}/{operator}")
                return {}
                
            self.logger.info(f"Available Google numbers: {google_info.get('Qty', 0)} at ${google_info.get('Price', 0)}")
            return google_info
            
        except Exception as e:
            self.logger.error(f"Failed to get available numbers: {str(e)}")
            raise FiveSimError(f"Failed to get available numbers: {str(e)}")
    
    def buy_phone_number(self, country: str = None, operator: str = None) -> PhoneOrder:
        """
        Purchase a phone number for Gmail verification
        
        Args:
            country: Country code (default: any)
            operator: Operator name (default: any)
            
        Returns:
            PhoneOrder object with phone number details
        """
        try:
            # Check balance first
            balance = self.get_balance()
            if balance < 0.5:  # Minimum balance check
                raise FiveSimError(f"Insufficient balance: ${balance}. Please top up your 5sim account.")
            
            # Check availability
            availability = self.get_available_numbers(country, operator)
            if not availability or availability.get('Qty', 0) == 0:
                raise FiveSimError("No phone numbers available for Google verification")
            
            country = country or self.default_country
            operator = operator or self.default_operator
            
            # Purchase number
            url = f"{self.base_url}/user/buy/activation/{country}/{operator}/{self.product}"
            response = self.session.get(url)
            response.raise_for_status()
            
            data = response.json()
            
            # Parse SMS messages
            sms_list = []
            for sms_data in data.get('sms', []) or []:
                if sms_data:  # Handle null SMS entries
                    sms_list.append(SMSMessage(
                        id=sms_data.get('id', 0),
                        created_at=sms_data.get('created_at', ''),
                        date=sms_data.get('date', ''),
                        sender=sms_data.get('sender', ''),
                        text=sms_data.get('text', ''),
                        code=sms_data.get('code', '')
                    ))
            
            order = PhoneOrder(
                id=data['id'],
                phone=data['phone'],
                operator=data['operator'],
                product=data['product'],
                price=data['price'],
                status=data['status'],
                expires=data['expires'],
                sms=sms_list,
                created_at=data['created_at'],
                country=data['country']
            )
            
            self.logger.info(f"Successfully purchased phone number: {order.phone} (Order ID: {order.id})")
            return order
            
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 400:
                error_msg = "No available phone numbers or insufficient balance"
            else:
                error_msg = f"HTTP error {e.response.status_code}"
            self.logger.error(f"Failed to buy phone number: {error_msg}")
            raise FiveSimError(f"Failed to buy phone number: {error_msg}")
        except Exception as e:
            self.logger.error(f"Failed to buy phone number: {str(e)}")
            raise FiveSimError(f"Failed to buy phone number: {str(e)}")
    
    def check_sms(self, order_id: int) -> PhoneOrder:
        """
        Check for SMS messages on a phone number order
        
        Args:
            order_id: Order ID from phone number purchase
            
        Returns:
            Updated PhoneOrder with any new SMS messages
        """
        try:
            url = f"{self.base_url}/user/check/{order_id}"
            response = self.session.get(url)
            response.raise_for_status()
            
            data = response.json()
            
            # Parse SMS messages
            sms_list = []
            for sms_data in data.get('sms', []) or []:
                if sms_data:  # Handle null SMS entries
                    sms_list.append(SMSMessage(
                        id=sms_data.get('id', 0),
                        created_at=sms_data.get('created_at', ''),
                        date=sms_data.get('date', ''),
                        sender=sms_data.get('sender', ''),
                        text=sms_data.get('text', ''),
                        code=sms_data.get('code', '')
                    ))
            
            order = PhoneOrder(
                id=data['id'],
                phone=data['phone'],
                operator=data['operator'],
                product=data['product'],
                price=data['price'],
                status=data['status'],
                expires=data['expires'],
                sms=sms_list,
                created_at=data['created_at'],
                country=data['country']
            )
            
            return order
            
        except Exception as e:
            self.logger.error(f"Failed to check SMS for order {order_id}: {str(e)}")
            raise FiveSimError(f"Failed to check SMS: {str(e)}")
    
    def wait_for_sms(self, order_id: int, timeout: int = None) -> Optional[str]:
        """
        Wait for SMS verification code
        
        Args:
            order_id: Order ID from phone number purchase
            timeout: Maximum wait time in seconds (default: 300)
            
        Returns:
            Verification code if received, None if timeout
        """
        timeout = timeout or self.max_wait_time
        start_time = time.time()
        
        self.logger.info(f"Waiting for SMS verification code (Order ID: {order_id})")
        
        while time.time() - start_time < timeout:
            try:
                order = self.check_sms(order_id)
                
                # Check if we have SMS messages
                if order.sms:
                    for sms in order.sms:
                        if sms.code:  # 5sim automatically extracts codes
                            self.logger.info(f"Received SMS verification code: {sms.code}")
                            return sms.code
                        elif sms.text:
                            # Fallback: extract code from text if 5sim didn't extract it
                            import re
                            code_match = re.search(r'\b\d{4,8}\b', sms.text)
                            if code_match:
                                code = code_match.group()
                                self.logger.info(f"Extracted verification code from SMS: {code}")
                                return code
                
                # Check order status
                if order.status in [OrderStatus.TIMEOUT.value, OrderStatus.CANCELED.value, OrderStatus.BANNED.value]:
                    self.logger.warning(f"Order {order_id} status changed to {order.status}")
                    break
                
                time.sleep(self.check_interval)
                
            except Exception as e:
                self.logger.error(f"Error while waiting for SMS: {str(e)}")
                time.sleep(self.check_interval)
        
        self.logger.warning(f"SMS verification code not received within {timeout} seconds")
        return None
    
    def finish_order(self, order_id: int) -> bool:
        """
        Mark order as finished (successful verification)
        
        Args:
            order_id: Order ID to finish
            
        Returns:
            True if successful
        """
        try:
            url = f"{self.base_url}/user/finish/{order_id}"
            response = self.session.get(url)
            response.raise_for_status()
            
            self.logger.info(f"Successfully finished order {order_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to finish order {order_id}: {str(e)}")
            return False
    
    def cancel_order(self, order_id: int) -> bool:
        """
        Cancel an order (if no SMS received)
        
        Args:
            order_id: Order ID to cancel
            
        Returns:
            True if successful
        """
        try:
            url = f"{self.base_url}/user/cancel/{order_id}"
            response = self.session.get(url)
            response.raise_for_status()
            
            self.logger.info(f"Successfully canceled order {order_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cancel order {order_id}: {str(e)}")
            return False


def load_fivesim_config(config_path: str = "grps/PyFiles/json/fivesim_config.json") -> Dict[str, Any]:
    """Load 5sim configuration from JSON file"""
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)

        # Validate required fields
        if config.get('enabled', False) and not config.get('api_key', ''):
            logging.warning("5sim is enabled but no API key provided")
            config['enabled'] = False

        return config

    except FileNotFoundError:
        logging.info("5sim config file not found, using defaults")
        # Return default configuration
        return {
            "api_key": "",
            "enabled": False,
            "default_country": "any",
            "default_operator": "any",
            "max_wait_time": 300,
            "check_interval": 10,
            "preferred_countries": ["us", "gb", "ca", "fr"],
            "fallback_to_manual": True,
            "auto_finish_orders": True,
            "min_balance_threshold": 1.0,
            "retry_attempts": 3,
            "error_handling": {
                "auto_cancel_on_timeout": True,
                "auto_retry_on_failure": True,
                "max_retry_attempts": 2
            }
        }
    except Exception as e:
        logging.error(f"Error loading 5sim config: {str(e)}")
        return {}


def save_fivesim_config(config: Dict[str, Any], config_path: str = "grps/PyFiles/json/fivesim_config.json") -> bool:
    """Save 5sim configuration to JSON file"""
    try:
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=4)
        return True
    except Exception as e:
        logging.error(f"Error saving 5sim config: {str(e)}")
        return False


class FiveSimManager:
    """
    High-level manager for 5sim integration with Gmail automation
    Provides retry logic, error handling, and configuration management
    """

    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.config = load_fivesim_config()
        self.client = None
        self.current_order = None

        if self.config.get('enabled', False):
            api_key = self.config.get('api_key', '')
            if api_key:
                self.client = FiveSimClient(api_key, self.logger)
            else:
                self.logger.error("5sim enabled but no API key configured")

    def is_available(self) -> bool:
        """Check if 5sim integration is available and properly configured"""
        return (
            self.config.get('enabled', False) and
            self.client is not None and
            self.config.get('api_key', '') != ''
        )

    def get_phone_number_for_gmail(self) -> Optional[str]:
        """
        Get a phone number for Gmail verification with retry logic

        Returns:
            Phone number string if successful, None if failed
        """
        if not self.is_available():
            self.logger.warning("5sim integration not available")
            return None

        max_attempts = self.config.get('retry_attempts', 3)
        retry_delay = self.config.get('retry_delay', 30)

        for attempt in range(max_attempts):
            try:
                self.logger.info(f"Attempting to purchase phone number (attempt {attempt + 1}/{max_attempts})")

                # Try preferred countries first
                preferred_countries = self.config.get('preferred_countries', ['any'])

                for country in preferred_countries:
                    try:
                        order = self.client.buy_phone_number(
                            country=country,
                            operator=self.config.get('default_operator', 'any')
                        )

                        self.current_order = order
                        self.logger.info(f"Successfully purchased phone number: {order.phone} from {country}")
                        return order.phone

                    except FiveSimError as e:
                        self.logger.warning(f"Failed to get number from {country}: {str(e)}")
                        continue

                # If all preferred countries failed, try 'any'
                if 'any' not in preferred_countries:
                    try:
                        order = self.client.buy_phone_number(country='any', operator='any')
                        self.current_order = order
                        self.logger.info(f"Successfully purchased phone number: {order.phone} from any country")
                        return order.phone
                    except FiveSimError as e:
                        self.logger.error(f"Failed to get number from any country: {str(e)}")

            except Exception as e:
                self.logger.error(f"Unexpected error getting phone number: {str(e)}")

            # Wait before retry (except on last attempt)
            if attempt < max_attempts - 1:
                self.logger.info(f"Waiting {retry_delay} seconds before retry...")
                time.sleep(retry_delay)

        self.logger.error("Failed to get phone number after all attempts")
        return None

    def wait_for_verification_code(self, timeout: int = None) -> Optional[str]:
        """
        Wait for SMS verification code for the current order

        Args:
            timeout: Maximum wait time in seconds

        Returns:
            Verification code if received, None if timeout or error
        """
        if not self.current_order or not self.client:
            self.logger.error("No active order or client not initialized")
            return None

        timeout = timeout or self.config.get('max_wait_time', 300)

        try:
            code = self.client.wait_for_sms(self.current_order.id, timeout)
            return code
        except Exception as e:
            self.logger.error(f"Error waiting for verification code: {str(e)}")
            return None

    def finish_current_order(self) -> bool:
        """Mark the current order as finished"""
        if not self.current_order or not self.client:
            return False

        try:
            success = self.client.finish_order(self.current_order.id)
            if success:
                self.current_order = None
            return success
        except Exception as e:
            self.logger.error(f"Error finishing order: {str(e)}")
            return False

    def cancel_current_order(self) -> bool:
        """Cancel the current order"""
        if not self.current_order or not self.client:
            return False

        try:
            success = self.client.cancel_order(self.current_order.id)
            if success:
                self.current_order = None
            return success
        except Exception as e:
            self.logger.error(f"Error canceling order: {str(e)}")
            return False

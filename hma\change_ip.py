from pywinauto import Application, Desktop
import time



def wait_for_window(title_pattern, timeout=90, interval=2):
    """Wait for window with retry mechanism"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            window = Desktop(backend="uia").window(title_re=title_pattern)
            if window.exists():
                print(f"Found window matching pattern: {title_pattern}")
                return window
        except Exception as e:
            print(f"Window not found yet: {e}")
        time.sleep(interval)
    raise TimeoutError(f"Window not found after {timeout} seconds: {title_pattern}")

def change_ip():
    try:
        print("Starting HMA VPN application...")
        vpn_app = Application(backend="uia").start(r'C:\Program Files\Privax\HMA VPN\Vpn.exe')
        time.sleep(5)
        print("Application started successfully")

        # Wait for main window
        print("Waiting for HMA window...")
        dialog = wait_for_window(".*HMA.*")
        dialog.set_focus()
        print("HMA window found and focused")

        # Find control panel with retry
        max_tries = 3
        for attempt in range(max_tries):
            try:
                print(f"Attempting to find control panel (attempt {attempt + 1}/{max_tries})")
                panel0 = dialog.child_window(control_type="Pane", found_index=0)
                if panel0.exists():
                    print("Control panel found")
                    break
            except Exception as e:
                print(f"Failed to find panel (attempt {attempt + 1}): {e}")
                if attempt == max_tries - 1:
                    raise
                time.sleep(20)

        # Find and verify Change IP button
        print("Looking for Change IP button...")
        changeIP = panel0.child_window(title="Change IP Address", control_type="Button")
        if not changeIP.exists():
            raise RuntimeError("Change IP button not found")
        if not changeIP.is_enabled():
            raise RuntimeError("Change IP button is disabled")
        print("Change IP button found and enabled")

        # Click button with verification
        print("Clicking Change IP button...")
        changeIP.click()
        print("Change IP button clicked")

        # Wait for IP change to complete
        time.sleep(5)
        print("IP change operation completed")

        # Verify successful change
        try:
            connected_btn = panel0.child_window(title="Disconnect", control_type="Button")
            if connected_btn.exists():
                print("Successfully verified connected state")
                return True
        except Exception as e:
            print(f"Failed to verify connected state: {e}")
            return False

    except Exception as e:
        print(f"Failed to change IP: {e}")
        return False
    finally:
        print("IP change operation finished")

from pywinauto import Application, Desktop
import time




def wait_for_window(title_pattern, timeout=30, interval=2):
    """Wait for window with retry mechanism"""
    print(f"Waiting for window matching pattern: {title_pattern}")
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            window = Desktop(backend="uia").window(title_re=title_pattern)
            if window.exists():
                print(f"Found window matching pattern: {title_pattern}")
                return window
        except Exception as e:
            print(f"Window not found yet: {e}")
        time.sleep(interval)
    raise TimeoutError(f"Window not found after {timeout} seconds: {title_pattern}")

def verify_connection(panel, max_wait=90):
    """Verify VPN connection status by checking for Disconnect button"""
    print("Verifying VPN connection...")
    start_time = time.time()
    while time.time() - start_time < max_wait:
        try:
            disconnect_button = panel.child_window(title="Disconnect", control_type="Button")
            if disconnect_button.exists():
                print("VPN connection verified - Disconnect button found")
                return True
        except Exception as e:
            print(f"Connection not verified yet: {e}")
        time.sleep(1)
    print("Could not verify connection - Disconnect button not found")
    return False


def turn_on_vpn():
    try:
        # Check if VPN is already running
        
        print("Starting HMA VPN application...")
        vpn_app = Application(backend="uia").start(r'C:\Program Files\Privax\HMA VPN\Vpn.exe')
        time.sleep(5)
        print("Application started successfully")


        print("Waiting for HMA window...")
        dialog = wait_for_window(".*HMA.*")
        dialog.set_focus()
        print("HMA window found and focused")


        max_tries = 3
        panel0 = None
        for attempt in range(max_tries):
            try:
                print(f"Attempting to find control panel (attempt {attempt + 1}/{max_tries})")
                panel0 = dialog.child_window(control_type="Pane", found_index=0)
                if panel0.exists():
                    print("Control panel found")
                    break
            except Exception as e:
                print(f"Failed to find panel (attempt {attempt + 1}): {e}")
                if attempt == max_tries - 1:
                    raise
                time.sleep(2)

        print("Looking for Connect/Disconnect button...")
        connect_button = panel0.child_window(title="Connect", control_type="Button")
        disconnect_button = panel0.child_window(title="Disconnect", control_type="Button")

        if disconnect_button.exists():
            print("VPN is already connected (Disconnect button present)")
            return True
        elif not connect_button.exists():
            raise RuntimeError("Neither Connect nor Disconnect button found")

        print("VPN is disconnected (Connect button present)")
        print("Clicking Connect button...")
        connect_button.click()
        print("Connect button clicked")

        if verify_connection(panel0):
            print("VPN connected successfully")
            return True
        else:
            print("Failed to verify VPN connection")
            return False

    except Exception as e:
        print(f"Failed to turn on VPN: {e}")
        return False
    finally:
        print("VPN operation finished")
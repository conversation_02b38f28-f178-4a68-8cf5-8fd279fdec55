#!/usr/bin/env python3
"""
Installation script for enhanced proxy management dependencies
"""

import subprocess
import sys
import os


def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}: {e}")
        return False


def check_package(package):
    """Check if a package is already installed"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False


def main():
    """Main installation function"""
    print("🚀 Installing enhanced proxy management dependencies...")
    
    # Required packages for enhanced proxy functionality
    required_packages = [
        "aiohttp",
        "asyncio",
        "requests",
        "selenium",
        "webdriver-manager",
        "undetected-chromedriver"
    ]
    
    # Optional packages for additional features
    optional_packages = [
        "selenium-wire",  # For advanced proxy integration
        "fake-useragent",  # For user agent rotation
        "python-dotenv"   # For environment variable management
    ]
    
    installed_count = 0
    failed_count = 0
    
    print("\n📦 Installing required packages...")
    for package in required_packages:
        if not check_package(package.replace("-", "_")):
            if install_package(package):
                installed_count += 1
            else:
                failed_count += 1
        else:
            print(f"✅ {package} is already installed")
    
    print("\n📦 Installing optional packages...")
    for package in optional_packages:
        if not check_package(package.replace("-", "_")):
            if install_package(package):
                installed_count += 1
            else:
                print(f"⚠️ Optional package {package} failed to install (not critical)")
        else:
            print(f"✅ {package} is already installed")
    
    print(f"\n📊 Installation Summary:")
    print(f"   Packages installed: {installed_count}")
    print(f"   Packages failed: {failed_count}")
    
    if failed_count == 0:
        print("\n🎉 All dependencies installed successfully!")
        print("You can now run the proxy setup script:")
        print("   python setup_proxy.py setup")
    else:
        print(f"\n⚠️ {failed_count} packages failed to install.")
        print("Please install them manually or check your internet connection.")
    
    return failed_count == 0


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)

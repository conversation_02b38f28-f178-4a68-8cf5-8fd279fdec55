#!/usr/bin/env python3
"""
Example of using CDP Mode with the updated SeleniumBase configuration

This example shows how to:
1. Create a browser with UC Mode enabled
2. Activate CDP Mode for maximum stealth
3. Use CDP methods for undetectable automation
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from updated_groups import EnhancedSeleniumBaseDriver
import time

def test_cdp_mode():
    """Test CDP Mode functionality"""
    
    # Create enhanced driver (UC Mode is now enabled by default)
    driver_instance = EnhancedSeleniumBaseDriver(
        email="<EMAIL>",
        password="test_password", 
        ua_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        index=0
    )
    
    try:
        print("✓ Enhanced SeleniumBase driver created with UC Mode")
        
        # Test basic navigation first (UC Mode)
        test_url = "https://intoli.com/blog/not-possible-to-block-chrome-headless/chrome-headless-test.html"
        driver_instance.browser.get(test_url)
        print(f"✓ Navigated to test page: {test_url}")
        
        time.sleep(3)
        
        # Now activate CDP Mode for maximum stealth
        print("\n--- Activating CDP Mode ---")
        cdp_activated = driver_instance.activate_cdp_mode()
        
        if cdp_activated:
            print("✓ CDP Mode activated successfully")
            
            # Now you can use CDP methods for undetectable automation
            # Example: Click using CDP (undetectable)
            try:
                # Check if we can access CDP methods
                if hasattr(driver_instance.browser, 'cdp'):
                    print("✓ CDP methods available")
                    
                    # Example CDP operations
                    title = driver_instance.browser.cdp.get_title()
                    print(f"✓ Page title via CDP: {title}")
                    
                    # Get text using CDP
                    try:
                        body_text = driver_instance.browser.cdp.get_text("body")
                        print(f"✓ Body text length via CDP: {len(body_text)} characters")
                    except Exception as e:
                        print(f"⚠ CDP get_text failed: {e}")
                    
                else:
                    print("⚠ CDP methods not directly available, but CDP Mode is active")
                    
            except Exception as e:
                print(f"⚠ CDP operations failed: {e}")
                
        else:
            print("✗ Failed to activate CDP Mode")
            
        # Test fingerprint detection
        print("\n--- Testing Fingerprint Detection ---")
        try:
            # Check webdriver property
            webdriver_result = driver_instance.browser.execute_script("return navigator.webdriver;")
            print(f"navigator.webdriver: {webdriver_result}")
            
            # Check user agent
            user_agent = driver_instance.browser.execute_script("return navigator.userAgent;")
            print(f"User agent: {user_agent[:50]}...")
            
            # Check if automation is detected
            automation_detected = driver_instance.browser.execute_script("""
                return window.chrome && window.chrome.runtime && window.chrome.runtime.onConnect;
            """)
            print(f"Chrome runtime detected: {automation_detected}")
            
        except Exception as e:
            print(f"⚠ Fingerprint test failed: {e}")
            
        print("\n--- Test completed ---")
        time.sleep(5)
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Cleanup
        try:
            driver_instance.browser.quit()
            print("✓ Browser closed")
        except:
            pass

def test_cdp_mode_with_url():
    """Test CDP Mode with direct URL activation"""
    
    driver_instance = EnhancedSeleniumBaseDriver(
        email="<EMAIL>",
        password="test_password", 
        ua_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        index=0
    )
    
    try:
        print("✓ Enhanced SeleniumBase driver created")
        
        # Activate CDP Mode with URL (this will navigate and activate CDP in one step)
        test_url = "https://bot.sannysoft.com/"
        print(f"\n--- Activating CDP Mode with URL: {test_url} ---")
        
        cdp_activated = driver_instance.activate_cdp_mode(test_url)
        
        if cdp_activated:
            print("✓ CDP Mode activated with URL")
            time.sleep(5)
            
            # Check detection results
            try:
                page_text = driver_instance.browser.execute_script("return document.body.innerText;")
                if "WebDriver" in page_text:
                    print("⚠ WebDriver detected in page content")
                else:
                    print("✓ WebDriver not detected in page content")
                    
            except Exception as e:
                print(f"⚠ Could not check page content: {e}")
                
        else:
            print("✗ Failed to activate CDP Mode with URL")
            
        time.sleep(5)
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        try:
            driver_instance.browser.quit()
            print("✓ Browser closed")
        except:
            pass

if __name__ == "__main__":
    print("=== CDP Mode Test 1: Basic Activation ===")
    test_cdp_mode()
    
    print("\n" + "="*50)
    print("=== CDP Mode Test 2: URL Activation ===")
    test_cdp_mode_with_url()
    
    print("\n" + "="*50)
    print("Tests completed!")
    print("\nNow your browser should show much better results on bot detection tests!")
    print("The 'Permissions' should show 'granted' instead of 'denied'")
    print("WebGL should work properly instead of showing 'no webgl context'")
